#!/bin/bash

# Comprehensive test suite for Silver Surfer gRPC server
echo "🧪 Silver Surfer gRPC Server Test Suite"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${YELLOW}Running: $test_name${NC}"
    echo "----------------------------------------"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ PASSED: $test_name${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAILED: $test_name${NC}"
        ((TESTS_FAILED++))
    fi
}

# Check prerequisites
echo "Checking prerequisites..."

# Check if server is running
if ! netstat -tuln | grep -q ":8111 "; then
    echo -e "${RED}❌ Server is not running on port 8111${NC}"
    echo "Please start the server first:"
    echo "  cd app && go run ."
    exit 1
fi

echo -e "${GREEN}✅ Server is running on port 8111${NC}"

# Install grpcurl if not available
if ! command -v grpcurl &> /dev/null; then
    echo "Installing grpcurl..."
    go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest
fi

# Run tests
echo -e "\n🚀 Starting test execution..."

# Test 1: Basic health check
run_test "Basic Health Check" "bash test-server-health.sh"

# Test 2: Service discovery
run_test "Service Discovery" "grpcurl -plaintext localhost:8111 list | grep -q SilverSurferService"

# Test 3: Method listing
run_test "Method Discovery" "grpcurl -plaintext localhost:8111 list client.silverSurfer.grpc.SilverSurferService | grep -q GetClusterUpgradeSummaryValidationResult"

# Test 4: grpcurl functional test
run_test "grpcurl Functional Test" "bash test-grpcurl.sh"

# Test 5: Go client test
if [ -f "test-client/main.go" ]; then
    run_test "Go Client Test" "cd test-client && go mod init test-client && go mod tidy && go run main.go"
fi

# Test 6: Load test (optional)
if [ "$1" = "--load-test" ]; then
    run_test "Load Test" "go run test-load.go"
fi

# Summary
echo -e "\n📊 Test Summary"
echo "==============="
echo -e "Tests passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Your Silver Surfer server is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please check the server configuration and logs.${NC}"
    exit 1
fi
