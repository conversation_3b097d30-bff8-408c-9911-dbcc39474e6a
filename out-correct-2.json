current cluster server version:-  1.30

Results for cluster at version 1.30 to 1.30
-------------------------------------------
[
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "lutho",
		"ResourceNamespace": "default",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "dashboard-pnpm-devtron-demo-ingress",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "dashboard-pnpm-ent-devtron-demo-ingress",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "deploy-poc-devtron-demo-ingress",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "deploy-poc-devtron-demo-ingress-internal",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "roll-poc-devtron-demo-ingress",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "roll-poc-devtron-demo-ingress-internal",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "rollout-devtron-demo-ingress",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "privoxy-test-shared-dcd-ent-1-ingress",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-1-ingress",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-10-ingress",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-2-ingress",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-3-ingress",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-4-ingress",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5-ingress",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-6-ingress",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-7-ingress",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-8-ingress",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-devtron-orchestrator-shared-dcd-ent-9-ingress",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1-ingress",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2-ingress",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "abhibhaw-ev-real-1-ingress",
		"ResourceNamespace": "ev-real-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-opencost-ingress",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent-ingress",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Ingress",
		"APIVersion": "networking.k8s.io/v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils-ingress",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/ingressClassName",
				"SchemaField": "",
				"Reason": "ingressClassName is the name of an IngressClass cluster resource. Ingress controller implementations use this field to know whether they should be serving this Ingress resource, by a transitive connection (controller -\u003e IngressClass -\u003e Ingress resource). Although the `kubernetes.io/ingress.class` annotation (simple constant name) was never formally defined, it was widely supported by Ingress controllers to create a direct binding between Ingress controller and Ingress resources. Newly created Ingress resources should prefer using the field. However, even though the annotation is officially deprecated, for backwards compatibility reasons, ingress controllers should still honor that annotation if present.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "app-test-fluentbit-fluent-bit",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "custom-app-apica-1-devtron-demo-node-exporter",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "custom-chart-contour-envoy",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "fluent-s-fluent-bit",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "fluent-test-fluent-bit",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "vgut-fluent-bit",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "fluent-bit-ent-8-test-1",
		"ResourceNamespace": "ent-8-test-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "DaemonSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-0412bff8-8a8e-40d8-923a-e5cfa3bbbe9b",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-071237a9-c685-4156-898b-33f20f4d2fdf",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-07736183-9a6b-4049-b16d-c937b49f94f1",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-2fc8f28e-52c7-430f-b1b7-fe7057ae0356",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-33a8d66f-1c8c-4633-8d00-892ef195800a",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-43e0ef80-4f34-4126-8903-72636b135901",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-4ce01bbb-aa56-40bf-82a2-007b8222ef3a",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-61da3880-0e49-4ba3-bc27-3c5479c6fedb",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-64ea248c-f314-4ac7-bdd5-ef44ce455fce",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-6ad1ea05-ca4d-4260-95a2-427ebd8c3f66",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-6dbbfc7e-bd77-4d00-a8b9-00783ae006ee",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-76270664-e04f-4106-bc6a-f034a5252b2b",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-7b842a0b-a296-484f-9acc-74bc39ffc60f",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-8ccd0a17-e774-4c8d-810a-2531890ae0b1",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-8eaab570-f3bf-4827-9755-e9232e3c5a4b",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-9cc7cf99-f751-48dd-bfef-5f5963e1cdc1",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-9db0b153-1c6a-4803-978c-726bcf53316f",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-a505ae6c-a3c2-4454-beec-0948eebb9163",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-a7ca46ff-3797-4ac3-bd68-f20d3a34bd19",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-abc75ea4-6c26-41b6-8116-d8ce2e554a9f",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-b61c4762-6e39-4fa4-9d63-6840d4ec8120",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-e8393642-bdc8-4167-8b59-9fb3f71b4045",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-eeede5f2-e587-4c7f-8ddd-f831f00cdad4",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "PersistentVolume",
		"APIVersion": "v1",
		"ResourceName": "pvc-f0d7bef1-8175-4d86-a6cb-98ee0db57cac",
		"ResourceNamespace": "undefined",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/persistentVolumeReclaimPolicy",
				"SchemaField": "",
				"Reason": "persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "lutho-5bb5b55967-dd782",
		"ResourceNamespace": "default",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "a-0-demo-test-5bc7f5cf66-pfp45",
		"ResourceNamespace": "demo-test",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "airflow-devtron-demo-scheduler-5fcb549b87-j7rhx",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "airflow-devtron-demo-scheduler-7d969bc678-js5f9",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "airflow-devtron-demo-web-547bf55dc6-qhp45",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "airflow-devtron-demo-web-54cf865547-skbfr",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "airflow-devtron-demo-worker-0",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "app-test-fluentbit-fluent-bit-5gvp7",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "app-test-fluentbit-fluent-bit-9jlk7",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "app-test-fluentbit-fluent-bit-b8225",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "app-test-fluentbit-fluent-bit-w75tz",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "app2-memcached-7987f59d7f-8cxfw",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argoa-pp-devtron-demo-cb667b5cd-56qwl",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "clone-1-devtron-demo-79d5996677-xpsln",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-app-apica-1-devtron-demo-node-exporter-d84nt",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-app-apica-1-devtron-demo-node-exporter-q6dbb",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-app-apica-1-devtron-demo-node-exporter-t57ck",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-app-apica-1-devtron-demo-node-exporter-txhnv",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-chart-contour-envoy-78vfw",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-chart-contour-envoy-gtml4",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "custom-chart-contour-envoy-ltrx7",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-s-fluent-bit-gqxpr",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-s-fluent-bit-qfr8w",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-s-fluent-bit-sgk88",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-test-fluent-bit-6fd5t",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-test-fluent-bit-f6wf9",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-test-fluent-bit-kp5cj",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "holmes-gpt-holmes-7d458544c-wl7n4",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "kb-daemonset-app-4xkpr",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "kb-daemonset-app-9p4rh",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "kb-daemonset-app-zr2bw",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "vgut-fluent-bit-hmxtb",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "vgut-fluent-bit-sfv8n",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "vgut-fluent-bit-tjg8g",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-1-23-zhhwv",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-1-7948b958fc-vprq9",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-1-68cd46df8-whmwx",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-1-5bf675ccb5-769pb",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-1-6564f64b47-vvb9f",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-1-8475b659d7-vvmwk",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-1-777b5b9fd9-5x258",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-1-7544988f79-dlfvn",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-ent-10-776cf5d7b8-hjjsx",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-10-14-wvq8p",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-10-28-4prsd",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-10-30-5nt2p",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-ent-4-779fc8c8d-48hxz",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-box-5746d69fb5-hhtz8",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-4-5d59558b69-s6pnl",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-4-6bd45f9ff7-22njp",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-4-c8cff9b7f-szbkc",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-4-6bccbd4d99-x5bzm",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-4-97f898d4f-j5cxr",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-4-5c546c9b5b-d4xx7",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-4-96f8b968f-fbrz9",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-4-c7697b48b-k2kfg",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-4-6459b9d755-z9qkp",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-5-788bbf5595-thrkm",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-5-6b68646677-zklgg",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-5-5cb7b8bb56-t2n2d",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-5-78bc44d578-6zzvm",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-5-76bd568466-tf6d2",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5-5cb7fd5db7-k62x6",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-ent-6-6d88765948-ncmhx",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-box-5746d69fb5-tbm76",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-6-6795f8598c-4qps7",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-6-7d6c88f799-wrs5s",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-6-757dd647cd-z8cn4",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-6-76d44cd8b4-x7bdw",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-6-684c4c55fb-nq2rk",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-6-5c779ff54-c5hzh",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-6-5754b8d689-7ppl5",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-6-8b4fcb898-8jzf5",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-6-7bcd5c84d6-4hgl7",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-ent-7-757b9d5cc8-tp8g2",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-box-5746d69fb5-5bqc4",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-7-5c99fb74b4-lm9p6",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-7-58f4d766c9-cwrbf",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-7-787fc4f46b-qwjbc",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-7-759c8d84b-vkv6v",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-7-5db9d599d4-qt4nm",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-7-7d5798b766-9gf6l",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-7-5f7fdd9595-bzr4r",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-ent-8-7d5cf85cf9-7rqtj",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-8-14-sjp2c",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-8-6984f497f5-s8wml",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-8-675566c6fc-z7qxk",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-8-84994c7d46-84bgs",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-8-7bbb7c4b4-78q86",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-8-7d5fbfd6dc-msvrt",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-8-6b58ff95f5-7cg2k",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-8-d578f9dcf-wcz7j",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-8-5c9d878875-whw5k",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-8-5f554f64fb-f2khc",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "sts-test-shared-dcd-ent-8-688955b99d-lbftt",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-ent-9-698956d895-h4fq4",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-box-6d88975788-v45p8",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-9-22-t5bhj",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-9-27-sctms",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-oss-1-58bc479cd7-wgcln",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-box-6d88975788-nnc69",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-oss-shared-dcd-oss-1-4-xn67z",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-1-6779fd4667-bhnp9",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-git-sensor-6cb49789d7-tsn2r",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-1-555c65d79d-rpz9p",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-1-74cc96ddc7-sfc7z",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-1-8c6bc9d5f-5nfsc",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-1-57d6674c79-xj9pv",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-1-557ff56d-hc2rs",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1-7f9946f966-q4n9x",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-oss-2-f4469d9dc-rwtd6",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-box-5746d69fb5-jxp6l",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cluster-clean-up-oss-shared-dcd-oss-2-10-wvmnh",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-2-6799c7dddf-rg5xl",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-git-sensor-65fd7cd687-mxntt",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-2-c6997f75c-v5p2x",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-2-7fcb548fdb-bk27s",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-2-8644bdcb6b-t82hq",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-2-dd559d76c-4ntdt",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-2-64c969dc84-8wf4r",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2-7c565b796-7xhh2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-application-controller-0",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-redis-5bbb5f494c-jc5rx",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-repo-server-7776c5788-h7g92",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-server-85cc66dd65-2j8bf",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "kyverno-cleanup-admission-reports-********-s4tmz",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "kyverno-cleanup-cluster-admission-reports-********-ggs2t",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "migrate-postgres-0",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "postgresql-postgresql-0",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-bit-ent-8-test-1-4zglr",
		"ResourceNamespace": "ent-8-test-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-bit-ent-8-test-1-d4m8q",
		"ResourceNamespace": "ent-8-test-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "fluent-bit-ent-8-test-1-p5vvq",
		"ResourceNamespace": "ent-8-test-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "helm-controller-6f558f6c5d-gzvw7",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "kustomize-controller-74fb56995-2x6t7",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "notification-controller-5d794dd575-tq77t",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "source-controller-6d597849c8-28g4f",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cost-exporter-shared-opencost-********-dnzwm",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cost-exporter-shared-opencost-********-bwzmc",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-cost-exporter-shared-opencost-********-gm7v6",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-opencost-5f87d464cb-f94r5",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "argocd-dex-server-shared-staging-9dcc5c957-7vgb8",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "devtron-nats-0",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "git-sensor-b45757db4-s69z4",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-casbin-dev-staging-ent-ccbd96c8b-spwws",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-dashboard-dev-staging-ent-b96b59d86-xrjcn",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-image-scanner-dev-staging-ent-7bcc889759-kwgrx",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubelink-dev-staging-ent-69bc9bff89-gtvgr",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-kubewatch-dev-staging-ent-ccf7dcdb5-qlcmb",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-lens-dev-staging-ent-7d64564b56-8sc5n",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-notifier-dev-staging-ent-5584f4979d-d98ls",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent-676b65cf6d-nsqzg",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "prakash1-test-env-5-5977d4544d-jgf7r",
		"ResourceNamespace": "test-env-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "alertmanager-shared-monitoring-stack-ku-alertmanager-0",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils-7bf45cc5ff-xt5jn",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-controller-manager-5745c4cd8-l2sbs",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-external-scaler-7fdfdb974f-bjn2c",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-external-scaler-7fdfdb974f-jkfb8",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-external-scaler-7fdfdb974f-mnjn5",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-interceptor-5bdf6c45bb-8jlmv",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-interceptor-5bdf6c45bb-ddtw4",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-add-ons-http-interceptor-5bdf6c45bb-snh2j",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-admission-webhooks-7f5f794854-6zw5l",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-operator-5d564674b9-hbc26",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "keda-operator-metrics-apiserver-5fd8648897-bzmn2",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "prometheus-shared-monitoring-stack-ku-prometheus-0",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-flux-automation-controller-847f878b44-29clw",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-flux-helm-controller-58bdd7b54b-mb7lh",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-flux-kustomize-controller-5869fb4d7d-wsp85",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-flux-notification-controller-7b7c947c55-fwvhr",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-flux-reflector-controller-984655dd8-6wv8g",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-flux-source-controller-65667df7fb-pjqfn",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-minio-0",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-minio-1",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-grafana-66ddd95ddc-2k2tq",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-ku-operator-75b4c6b594-2sgbp",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-kube-state-metrics-7cd5c56bb9-rz2j9",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-9v9z6",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-9wzdx",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-b8svr",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-bdkw6",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-dgqcs",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-qkk6w",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-monitoring-stack-prometheus-node-exporter-vvddq",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-nginx-controller-8688f96575-42k55",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-nginx-controller-8688f96575-dlvhm",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "shared-nginx-controller-8688f96575-jqpzv",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "winter-soldier-c75fc9f77-ww2gk",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "workflow-controller-647488f868-zk6f4",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "backend-675b88bd79-q8b6g",
		"ResourceNamespace": "webapp",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Pod",
		"APIVersion": "v1",
		"ResourceName": "frontend-6f8d849496-bssz7",
		"ResourceNamespace": "webapp",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "lutho",
		"ResourceNamespace": "default",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "a-0-demo-test",
		"ResourceNamespace": "demo-test",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-scheduler",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-web",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "app2-memcached",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argoa-pp-devtron-demo",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "clone-1-devtron-demo",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "holmes-gpt-holmes",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-2",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-2",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-2",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-3",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-3",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "sts-test-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-git-sensor",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-git-sensor",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-redis",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-repo-server",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-server",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "helm-controller",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "kustomize-controller",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "notification-controller",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "source-controller",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-opencost",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "ssh-client-sh-playground",
		"ResourceNamespace": "sh-playground",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-shared-staging",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "git-sensor",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "prakash1-test-env-5",
		"ResourceNamespace": "test-env-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-controller-manager",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-external-scaler",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-admission-webhooks",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator-metrics-apiserver",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-automation-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-helm-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-kustomize-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-notification-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-reflector-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-source-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-grafana",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-ku-operator",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-kube-state-metrics",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "winter-soldier",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "workflow-controller",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "backend",
		"ResourceNamespace": "webapp",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Deployment",
		"APIVersion": "apps/v1",
		"ResourceName": "frontend",
		"ResourceNamespace": "webapp",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-1-23",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-1-********",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-10-30",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-10-********",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-2-********",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-3-********",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-4-********",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-5-********",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-6-********",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-7-********",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-8-14",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-8-********",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-cluster-clean-up-shared-dcd-ent-9-27",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-9-********",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "app-manual-sync-job",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "kyverno-cleanup-admission-reports-********",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "kyverno-cleanup-cluster-admission-reports-********",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "scoop-action-7icces6xh8",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "Job",
		"APIVersion": "batch/v1",
		"ResourceName": "scoop-action-ypg6d9gtag",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-1",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-10",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-2",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-3",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-4",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-5",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "shared-ent-chart-sync-shared-dcd-ent-9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "kyverno-cleanup-admission-reports",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "CronJob",
		"APIVersion": "batch/v1",
		"ResourceName": "kyverno-cleanup-cluster-admission-reports",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/jobTemplate/spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "lutho-5bb5b55967",
		"ResourceNamespace": "default",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "lutho-79dc49d745",
		"ResourceNamespace": "default",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "lutho-f55885848",
		"ResourceNamespace": "default",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "a-0-demo-test-5bc7f5cf66",
		"ResourceNamespace": "demo-test",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-scheduler-5fcb549b87",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-scheduler-7d969bc678",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-web-547bf55dc6",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-web-54cf865547",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "app2-memcached-5cc6889948",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "app2-memcached-69fbd7b75c",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "app2-memcached-76d6f567f9",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "app2-memcached-7987f59d7f",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "app2-memcached-884f5884c",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argoa-pp-devtron-demo-cb667b5cd",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "clone-1-devtron-demo-595b959557",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "clone-1-devtron-demo-6bf49bcbcc",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "clone-1-devtron-demo-748d6ddd77",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "clone-1-devtron-demo-79d5996677",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "holmes-gpt-holmes-7d458544c",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-1-5bd4cd8bb9",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-1-6bfb8dcdf",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-1-7948b958fc",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-1-d7b647d65",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-1-5b8ff6fcf8",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-1-5bdb96bb",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-1-68cd46df8",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-1-6fd4485794",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-1-5bf675ccb5",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-1-6564f64b47",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-1-8475b659d7",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-1-777b5b9fd9",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-1-559d645ff9",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-1-5dfc4c67f9",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-1-7544988f79",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-1-86cdb44b79",
		"ResourceNamespace": "devtroncd-ent-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-10-776cf5d7b8",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-10-78cf447df7",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-10-7557ff6c55",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-10-6479cb9b9b",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-10-5bb74c67b",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-10-54bb58f79f",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-10-74687c7bf5",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-10-778d698c67",
		"ResourceNamespace": "devtroncd-ent-10",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-2-5847f9847c",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-2-5f8dffdb4b",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-2-64cf94bb8c",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-2-664cfc6cd5",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-2-756fdfcf77",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-2-8f9bf5b9b",
		"ResourceNamespace": "devtroncd-ent-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-3-5c665f669d",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-3-79f9d75bc",
		"ResourceNamespace": "devtroncd-ent-3",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-4-779fc8c8d",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-4-5d59558b69",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-4-5869bdc6d9",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-4-66b464d877",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-4-6844f59ff7",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-4-6bd45f9ff7",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-4-9bff469d7",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-4-c8cff9b7f",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-4-6bccbd4d99",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-4-9846544cf",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-4-97f898d4f",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-4-5c546c9b5b",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-4-96f8b968f",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-4-c7697b48b",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-4-6459b9d755",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-4-667bb9575",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-4-74d584fd7f",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-4-7cc87c98ff",
		"ResourceNamespace": "devtroncd-ent-4",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-5-788bbf5595",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-5-6b68646677",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-5-5c9ff54458",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-5-5cb7b8bb56",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-5-8495595ff8",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-5-78bc44d578",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-5-76bd568466",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5-5cb7bf6597",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5-5cb7fd5db7",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5-7d5f47fbb6",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-5-84cb968978",
		"ResourceNamespace": "devtroncd-ent-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-6-6d88765948",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-6-6795f8598c",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-6-54b7bc47cf",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-6-5548bb757f",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-6-77d77f4859",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-6-7d6c88f799",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-6-695788c99d",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-6-757dd647cd",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-6-76bb697948",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-6-bd8447858",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-6-76d44cd8b4",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-6-684c4c55fb",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-6-84877d86b",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-6-5c779ff54",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-6-5754b8d689",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-6-8b4fcb898",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-6-5fb79b6bd6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-6-66989f4cfc",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-6-7bcd5c84d6",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-6-cdc45d458",
		"ResourceNamespace": "devtroncd-ent-6",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-7-5c874b848",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-7-757b9d5cc8",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-7-5c99fb74b4",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-7-58f4d766c9",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-7-6b9bcdd595",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-7-849c88764f",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-7-8f88fc895",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-7-5bf4f74864",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-7-68d6444b84",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-7-787fc4f46b",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-7-79cb6fbd4b",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-7-58c55749f8",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-7-5c655976df",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-7-7b4b95b6d6",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-7-84b57567c8",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-7-759c8d84b",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-7-79fc776d54",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-7-7bdfdfdb7c",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-7-9c44ff6fd",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-7-5c997776c7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-7-5db9d599d4",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-7-64d67f488f",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-7-747bf889cf",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-7-59fc76df5d",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-7-788c5bddf4",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-7-7cb46d79f8",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-7-7d5798b766",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-7-598c4dff7c",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-7-5f7fdd9595",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-7-746f4f768f",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-7-7c68d6987c",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-7-66dc9798f6",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-7-7b55f468fc",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-7-7dd8b548d7",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-7-86c8d6b65d",
		"ResourceNamespace": "devtroncd-ent-7",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-8-7d5cf85cf9",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-8-6984f497f5",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-8-74f5c97f95",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-8-7564c7fdd5",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-8-796d65d4b6",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-8-554b87c7f7",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-8-675566c6fc",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-8-7649747cf7",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-8-7dd9ddc55c",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-8-564d85fdd7",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-8-67c59545c9",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-8-84994c7d46",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-8-87bcb84d",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-8-5658f75d45",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-8-64f6fcf857",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-8-7bbb7c4b4",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-8-db64d77b8",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-8-6d446964f7",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-8-7d5fbfd6dc",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-8-54d7dc89d9",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-8-645f9d9977",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-8-6b58ff95f5",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-8-84b5b46b79",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-8-55c7fc5c48",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-8-5964fbb58c",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-8-6686485c4d",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-8-d578f9dcf",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-8-55b554756c",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-8-5c9d878875",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-8-667dc46c57",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-8-7dfbfb84d9",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-8-5f554f64fb",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-8-6678f78544",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-8-745c669d68",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-8-955c76684",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "sts-test-shared-dcd-ent-8-5945dd8886",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "sts-test-shared-dcd-ent-8-688955b99d",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "sts-test-shared-dcd-ent-8-6946d9c96",
		"ResourceNamespace": "devtroncd-ent-8",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-ent-9-698956d895",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-9-55c867b6f",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-9-568d885477",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-9-7fd7d54f5",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-shared-dcd-ent-9-fb5d4b68f",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-9-646689b597",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-9-6f5698cbdc",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-9-8c7766864",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-shared-dcd-ent-9-8c8f7c8b4",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-9-57d564f496",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-9-646d8cc8cb",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-9-65847d4958",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-git-sensor-shared-dcd-ent-9-75f75658b",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-9-5d997dd655",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-9-6c746685cf",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-9-78788c56c7",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-shared-dcd-ent-9-7db86d4d99",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-9-57f448cdb9",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-9-7f6778c5cc",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-9-7fd79d56c",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-shared-dcd-ent-9-847dcd998c",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-9-5f47cd756b",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-9-6cd445b494",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-9-7f6678c4bb",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-shared-dcd-ent-9-9d5bc5c64",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-9-57c8468b5b",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-9-75db644976",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-9-9cb579d6b",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-shared-dcd-ent-9-9f86b8f96",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-9-5449db5c68",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-9-64468ddd76",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-9-6db875f4db",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-shared-dcd-ent-9-758dbdb46d",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-9-5589c4c5b4",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-9-6475dbdcc6",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-9-**********",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-shared-dcd-ent-9-77d489cc66",
		"ResourceNamespace": "devtroncd-ent-9",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-oss-1-58bc479cd7",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-1-64c5c4c8dd",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-1-6779fd4667",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-git-sensor-6cb49789d7",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-1-555c65d79d",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-1-74cc96ddc7",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-1-8c6bc9d5f",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-1-57d6674c79",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-1-557ff56d",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1-7f9946f966",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1-bf65df9c4",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1-f4dc56cbb",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-1-fb79567c5",
		"ResourceNamespace": "devtroncd-oss-1",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-oss-2-f4469d9dc",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-2-6799c7dddf",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-dashboard-shared-dcd-oss-2-679f4df8d5",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-git-sensor-65fd7cd687",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-git-sensor-966d46585",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-2-c6997f75c",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-image-scanner-shared-dcd-oss-2-f47cbcd57",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-2-766885bb4d",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubelink-shared-dcd-oss-2-7fcb548fdb",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-2-6549fd99b8",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-kubewatch-shared-dcd-oss-2-8644bdcb6b",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-2-6d6fd4f555",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-lens-shared-dcd-oss-2-dd559d76c",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-2-64c969dc84",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-notifier-shared-dcd-oss-2-b567c684b",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2-68c8769bb8",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2-78f49cd86d",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2-7c565b796",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-oss-orchestrator-shared-dcd-oss-2-84b967b9b4",
		"ResourceNamespace": "devtroncd-oss-2",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-redis-5bbb5f494c",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-repo-server-7776c5788",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-server-85cc66dd65",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "helm-controller-6f558f6c5d",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "kustomize-controller-74fb56995",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "notification-controller-5d794dd575",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "source-controller-6d597849c8",
		"ResourceNamespace": "flux-system",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-opencost-5f87d464cb",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-opencost-b9b97758d",
		"ResourceNamespace": "opencost",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "ssh-client-sh-playground-577558fd6f",
		"ResourceNamespace": "sh-playground",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "ssh-client-sh-playground-6dbf55d55d",
		"ResourceNamespace": "sh-playground",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-shared-staging-687869ffdc",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-dex-server-shared-staging-9dcc5c957",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "git-sensor-54c69c5957",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "git-sensor-57cd98bd88",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "git-sensor-9bf889fcb",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "git-sensor-b45757db4",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-dev-staging-ent-5fd7d69b7d",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-dev-staging-ent-789755b46b",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-dev-staging-ent-84fc7c9498",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-casbin-dev-staging-ent-ccbd96c8b",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-dev-staging-ent-6474f64f6f",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-dev-staging-ent-68f6bc796f",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-dev-staging-ent-6f89f976df",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-dashboard-dev-staging-ent-b96b59d86",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-dev-staging-ent-587b79bf9",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-dev-staging-ent-5cb9d5f86c",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-dev-staging-ent-7bcc889759",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-image-scanner-dev-staging-ent-fb5d7dc99",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-dev-staging-ent-5b999f8477",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-dev-staging-ent-69bc9bff89",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-dev-staging-ent-7574bbb6b9",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubelink-dev-staging-ent-76bcfd7fd8",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-dev-staging-ent-584bc7b56f",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-dev-staging-ent-6d9d6779cc",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-dev-staging-ent-74bc5469",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-kubewatch-dev-staging-ent-ccf7dcdb5",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-dev-staging-ent-579f49f658",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-dev-staging-ent-75cd7788c9",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-dev-staging-ent-7d64564b56",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-lens-dev-staging-ent-959b9b6b",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-dev-staging-ent-5584f4979d",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-dev-staging-ent-566dc6799d",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-dev-staging-ent-5d7d9b59f8",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-notifier-dev-staging-ent-6b5f86f57d",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent-5bb956dbb4",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent-676b65cf6d",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent-d4675b6fb",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-ent-orchestrator-dev-staging-ent-f4bf9c776",
		"ResourceNamespace": "shared-stage-dcd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "prakash1-test-env-5-5977d4544d",
		"ResourceNamespace": "test-env-5",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils-655974658d",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils-6d85b697bb",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils-7bf45cc5ff",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "dashboard-storybook-shared-cd-utils-9b59db8d",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-controller-manager-548b78bccf",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-controller-manager-5745c4cd8",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-controller-manager-6d457b4685",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-controller-manager-867497fd64",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-external-scaler-55bb956fd5",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-external-scaler-68bfc57bbd",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-external-scaler-7fdfdb974f",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-5548b64786",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-5bdf6c45bb",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-657ccfbb6c",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-677b98c85",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-6894cc778c",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-76b7c95bc5",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-78cb48cfc",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-7b8c8d665f",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-7cfd6fbcd4",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-7fd85f4758",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-add-ons-http-interceptor-d8c4957db",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-admission-webhooks-5887b5dcc4",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-admission-webhooks-58d6554866",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-admission-webhooks-7f5f794854",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator-5d564674b9",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator-7bd8ff9445",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator-86d99569b",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator-metrics-apiserver-5747569f8d",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "keda-operator-metrics-apiserver-5fd8648897",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-automation-controller-847f878b44",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-helm-controller-58bdd7b54b",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-kustomize-controller-5869fb4d7d",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-notification-controller-7b7c947c55",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-reflector-controller-984655dd8",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-flux-source-controller-65667df7fb",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-grafana-66ddd95ddc",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-ku-operator-75b4c6b594",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-monitoring-stack-kube-state-metrics-7cd5c56bb9",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-6b94ddf595",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-768c6569c8",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-79cbb5dbf9",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-7bc96fb9d6",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-7bfb9f7855",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-7cf57c7d9d",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-8688f96575",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-nginx-controller-b8fd5bdcd",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "winter-soldier-545cd48654",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "winter-soldier-5b5fd95945",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "winter-soldier-78d9cc7486",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "winter-soldier-8684cf5585",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "winter-soldier-c75fc9f77",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "workflow-controller-647488f868",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "backend-675b88bd79",
		"ResourceNamespace": "webapp",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "ReplicaSet",
		"APIVersion": "apps/v1",
		"ResourceName": "frontend-6f8d849496",
		"ResourceNamespace": "webapp",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-postgresql",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-redis-master",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "airflow-devtron-demo-worker",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "alertmanager-prometheus-alertmanager",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "prometheus-prometheus-prometheus",
		"ResourceNamespace": "devtron-demo",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "argocd-application-controller",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "migrate-postgres",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "postgresql-postgresql",
		"ResourceNamespace": "devtroncd",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "alertmanager-shared-monitoring-stack-ku-alertmanager",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "prometheus-shared-monitoring-stack-ku-prometheus",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	},
	{
		"FileName": "",
		"Kind": "StatefulSet",
		"APIVersion": "apps/v1",
		"ResourceName": "shared-minio",
		"ResourceNamespace": "utils",
		"Deleted": false,
		"Deprecated": false,
		"LatestAPIVersion": "",
		"IsVersionSupported": 0,
		"ErrorsForOriginal": null,
		"ErrorsForLatest": null,
		"DeprecationForOriginal": [
			{
				"Path": "spec/template/spec/serviceAccount",
				"SchemaField": "",
				"Reason": "DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.",
				"Origin": null
			}
		],
		"DeprecationForLatest": null
	}
]
caught sig: interrupt