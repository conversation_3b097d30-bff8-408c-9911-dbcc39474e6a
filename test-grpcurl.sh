#!/bin/bash

# Test Silver Surfer gRPC server using grpcurl
echo "🧪 Testing Silver Surfer gRPC Server with grpcurl"

SERVER="localhost:8111"

# Check if grpcurl is installed
if ! command -v grpcurl &> /dev/null; then
    echo "❌ grpcurl is not installed. Installing..."
    go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest
    if ! command -v grpcurl &> /dev/null; then
        echo "❌ Failed to install grpcurl. Please install manually."
        exit 1
    fi
fi

echo "✅ grpcurl is available"

# Test 1: List available services
echo -e "\n🔍 Test 1: Discovering available services..."
grpcurl -plaintext $SERVER list

# Test 2: List methods for SilverSurferService
echo -e "\n🔍 Test 2: Listing SilverSurferService methods..."
grpcurl -plaintext $SERVER list client.silverSurfer.grpc.SilverSurferService

# Test 3: Describe the service
echo -e "\n🔍 Test 3: Describing the service..."
grpcurl -plaintext $SERVER describe client.silverSurfer.grpc.SilverSurferService

# Test 4: Make a test request
echo -e "\n🧪 Test 4: Making a test request..."
cat << 'EOF' > /tmp/test-request.json
{
  "targetK8sVersion": "1.25",
  "clusterConfig": {
    "apiServerUrl": "https://test-cluster.example.com",
    "clusterId": 1,
    "clusterName": "test-cluster",
    "insecureSkipTLSVerify": true,
    "remoteConnectionConfig": {
      "remoteConnectionMethod": "DIRECT"
    }
  }
}
EOF

echo "Sending test request..."
grpcurl -plaintext -d @/tmp/test-request.json $SERVER client.silverSurfer.grpc.SilverSurferService/GetClusterUpgradeSummaryValidationResult

# Cleanup
rm -f /tmp/test-request.json

echo -e "\n✅ grpcurl tests completed!"
