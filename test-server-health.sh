#!/bin/bash

# Test script to check if Silver Surfer gRPC server is running
echo "Testing Silver Surfer gRPC Server Health..."

# Check if server is listening on port 8111
if netstat -tuln | grep -q ":8111 "; then
    echo "✅ Server is listening on port 8111"
else
    echo "❌ Server is NOT listening on port 8111"
    echo "Make sure the server is running with: cd app && go run ."
    exit 1
fi

# Test gRPC connection using grpcurl (if available)
if command -v grpcurl &> /dev/null; then
    echo "Testing gRPC service discovery..."
    grpcurl -plaintext localhost:8111 list
    echo "Testing service methods..."
    grpcurl -plaintext localhost:8111 list client.silverSurfer.grpc.SilverSurferService
else
    echo "⚠️  grpcurl not found. Install it for better testing: go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest"
fi

echo "Health check completed!"
