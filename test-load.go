package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	
	pb "github.com/devtron-labs/silver-surfer/app/grpc"
)

func main() {
	const (
		serverAddr = "localhost:8111"
		numClients = 5
		requestsPerClient = 10
	)

	fmt.Printf("🚀 Starting load test with %d clients, %d requests each\n", numClients, requestsPerClient)

	var wg sync.WaitGroup
	results := make(chan TestResult, numClients*requestsPerClient)

	startTime := time.Now()

	// Launch concurrent clients
	for i := 0; i < numClients; i++ {
		wg.Add(1)
		go func(clientID int) {
			defer wg.Done()
			runClient(clientID, requestsPerClient, serverAddr, results)
		}(i)
	}

	// Wait for all clients to complete
	wg.Wait()
	close(results)

	// Collect and analyze results
	var totalRequests, successfulRequests, failedRequests int
	var totalDuration time.Duration

	for result := range results {
		totalRequests++
		if result.Success {
			successfulRequests++
		} else {
			failedRequests++
		}
		totalDuration += result.Duration
	}

	endTime := time.Now()
	totalTestTime := endTime.Sub(startTime)

	// Print results
	fmt.Printf("\n📊 Load Test Results:\n")
	fmt.Printf("Total requests: %d\n", totalRequests)
	fmt.Printf("Successful: %d (%.1f%%)\n", successfulRequests, float64(successfulRequests)/float64(totalRequests)*100)
	fmt.Printf("Failed: %d (%.1f%%)\n", failedRequests, float64(failedRequests)/float64(totalRequests)*100)
	fmt.Printf("Total test time: %v\n", totalTestTime)
	fmt.Printf("Average request duration: %v\n", totalDuration/time.Duration(totalRequests))
	fmt.Printf("Requests per second: %.2f\n", float64(totalRequests)/totalTestTime.Seconds())
}

type TestResult struct {
	Success  bool
	Duration time.Duration
	Error    error
}

func runClient(clientID, numRequests int, serverAddr string, results chan<- TestResult) {
	// Connect to server
	conn, err := grpc.Dial(serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Printf("Client %d: Failed to connect: %v", clientID, err)
		for i := 0; i < numRequests; i++ {
			results <- TestResult{Success: false, Error: err}
		}
		return
	}
	defer conn.Close()

	client := pb.NewSilverSurferServiceClient(conn)

	// Make requests
	for i := 0; i < numRequests; i++ {
		start := time.Now()
		
		request := &pb.ClusterUpgradeRequest{
			TargetK8SVersion: "1.25",
			ClusterConfig: &pb.ClusterConfig{
				ApiServerUrl:          fmt.Sprintf("https://test-cluster-%d.example.com", clientID),
				ClusterId:            int32(clientID*100 + i),
				ClusterName:          fmt.Sprintf("test-cluster-%d-%d", clientID, i),
				InsecureSkipTLSVerify: true,
			},
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		_, err := client.GetClusterUpgradeSummaryValidationResult(ctx, request)
		cancel()

		duration := time.Since(start)
		
		if err != nil {
			results <- TestResult{Success: false, Duration: duration, Error: err}
		} else {
			results <- TestResult{Success: true, Duration: duration}
		}
	}
}
