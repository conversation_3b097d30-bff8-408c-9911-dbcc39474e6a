/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	v1 "k8s.io/api/core/v1"
)

// ContainerResizePolicyApplyConfiguration represents an declarative configuration of the ContainerResizePolicy type for use
// with apply.
type ContainerResizePolicyApplyConfiguration struct {
	ResourceName  *v1.ResourceName                `json:"resourceName,omitempty"`
	RestartPolicy *v1.ResourceResizeRestartPolicy `json:"restartPolicy,omitempty"`
}

// ContainerResizePolicyApplyConfiguration constructs an declarative configuration of the ContainerResizePolicy type for use with
// apply.
func ContainerResizePolicy() *ContainerResizePolicyApplyConfiguration {
	return &ContainerResizePolicyApplyConfiguration{}
}

// WithResourceName sets the ResourceName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ResourceName field is set to the value of the last call.
func (b *ContainerResizePolicyApplyConfiguration) WithResourceName(value v1.ResourceName) *ContainerResizePolicyApplyConfiguration {
	b.ResourceName = &value
	return b
}

// WithRestartPolicy sets the RestartPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RestartPolicy field is set to the value of the last call.
func (b *ContainerResizePolicyApplyConfiguration) WithRestartPolicy(value v1.ResourceResizeRestartPolicy) *ContainerResizePolicyApplyConfiguration {
	b.RestartPolicy = &value
	return b
}
