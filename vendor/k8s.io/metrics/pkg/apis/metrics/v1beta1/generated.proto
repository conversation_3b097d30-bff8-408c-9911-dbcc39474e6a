/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.metrics.pkg.apis.metrics.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/api/resource/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/metrics/pkg/apis/metrics/v1beta1";

// ContainerMetrics sets resource usage metrics of a container.
message ContainerMetrics {
  // Container name corresponding to the one from pod.spec.containers.
  optional string name = 1;

  // The memory usage is the memory working set.
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> usage = 2;
}

// NodeMetrics sets resource usage metrics of a node.
message NodeMetrics {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The following fields define time interval from which metrics were
  // collected from the interval [Timestamp-Window, Timestamp].
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time timestamp = 2;

  optional k8s.io.apimachinery.pkg.apis.meta.v1.Duration window = 3;

  // The memory usage is the memory working set.
  map<string, k8s.io.apimachinery.pkg.api.resource.Quantity> usage = 4;
}

// NodeMetricsList is a list of NodeMetrics.
message NodeMetricsList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of node metrics.
  repeated NodeMetrics items = 2;
}

// PodMetrics sets resource usage metrics of a pod.
message PodMetrics {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // The following fields define time interval from which metrics were
  // collected from the interval [Timestamp-Window, Timestamp].
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time timestamp = 2;

  optional k8s.io.apimachinery.pkg.apis.meta.v1.Duration window = 3;

  // Metrics for all containers are collected within the same time window.
  repeated ContainerMetrics containers = 4;
}

// PodMetricsList is a list of PodMetrics.
message PodMetricsList {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // List of pod metrics.
  repeated PodMetrics items = 2;
}

