/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.networking.v1alpha1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/networking/v1alpha1";

// IPAddress represents a single IP of a single IP Family. The object is designed to be used by APIs
// that operate on IP addresses. The object is used by the Service core API for allocation of IP addresses.
// An IP address can be represented in different formats, to guarantee the uniqueness of the IP,
// the name of the object is the IP address in canonical format, four decimal digits separated
// by dots suppressing leading zeros for IPv4 and the representation defined by RFC 5952 for IPv6.
// Valid: *********** or 2001:db8::1 or 2001:db8:aaaa:bbbb:cccc:dddd:eeee:1
// Invalid: ********* or 2001:db8:0:0:0::1
message IPAddress {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the desired state of the IPAddress.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional IPAddressSpec spec = 2;
}

// IPAddressList contains a list of IPAddress.
message IPAddressList {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of IPAddresses.
  repeated IPAddress items = 2;
}

// IPAddressSpec describe the attributes in an IP Address.
message IPAddressSpec {
  // ParentRef references the resource that an IPAddress is attached to.
  // An IPAddress must reference a parent object.
  // +required
  optional ParentReference parentRef = 1;
}

// ParentReference describes a reference to a parent object.
message ParentReference {
  // Group is the group of the object being referenced.
  // +optional
  optional string group = 1;

  // Resource is the resource of the object being referenced.
  // +required
  optional string resource = 2;

  // Namespace is the namespace of the object being referenced.
  // +optional
  optional string namespace = 3;

  // Name is the name of the object being referenced.
  // +required
  optional string name = 4;
}

// ServiceCIDR defines a range of IP addresses using CIDR format (e.g. ***********/24 or 2001:db2::/64).
// This range is used to allocate ClusterIPs to Service objects.
message ServiceCIDR {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec is the desired state of the ServiceCIDR.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ServiceCIDRSpec spec = 2;

  // status represents the current state of the ServiceCIDR.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
  // +optional
  optional ServiceCIDRStatus status = 3;
}

// ServiceCIDRList contains a list of ServiceCIDR objects.
message ServiceCIDRList {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // items is the list of ServiceCIDRs.
  repeated ServiceCIDR items = 2;
}

// ServiceCIDRSpec define the CIDRs the user wants to use for allocating ClusterIPs for Services.
message ServiceCIDRSpec {
  // CIDRs defines the IP blocks in CIDR notation (e.g. "***********/24" or "2001:db8::/64")
  // from which to assign service cluster IPs. Max of two CIDRs is allowed, one of each IP family.
  // This field is immutable.
  // +optional
  repeated string cidrs = 1;
}

// ServiceCIDRStatus describes the current state of the ServiceCIDR.
message ServiceCIDRStatus {
  // conditions holds an array of metav1.Condition that describe the state of the ServiceCIDR.
  // Current service state
  // +optional
  // +patchMergeKey=type
  // +patchStrategy=merge
  // +listType=map
  // +listMapKey=type
  repeated k8s.io.apimachinery.pkg.apis.meta.v1.Condition conditions = 1;
}

