/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.authentication.v1beta1;

import "k8s.io/api/authentication/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/authentication/v1beta1";

// ExtraValue masks the value so protobuf can generate
// +protobuf.nullable=true
// +protobuf.options.(gogoproto.goproto_stringer)=false
message ExtraValue {
  // items, if empty, will result in an empty slice

  repeated string items = 1;
}

// SelfSubjectReview contains the user information that the kube-apiserver has about the user making this request.
// When using impersonation, users will receive the user info of the user being impersonated.  If impersonation or
// request header authentication is used, any extra keys will have their case ignored and returned as lowercase.
message SelfSubjectReview {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Status is filled in by the server with the user attributes.
  optional SelfSubjectReviewStatus status = 2;
}

// SelfSubjectReviewStatus is filled by the kube-apiserver and sent back to a user.
message SelfSubjectReviewStatus {
  // User attributes of the user making this request.
  // +optional
  optional k8s.io.api.authentication.v1.UserInfo userInfo = 1;
}

// TokenReview attempts to authenticate a token to a known user.
// Note: TokenReview requests may be cached by the webhook token authenticator
// plugin in the kube-apiserver.
message TokenReview {
  // Standard object's metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated
  optional TokenReviewSpec spec = 2;

  // Status is filled in by the server and indicates whether the token can be authenticated.
  // +optional
  optional TokenReviewStatus status = 3;
}

// TokenReviewSpec is a description of the token authentication request.
message TokenReviewSpec {
  // Token is the opaque bearer token.
  // +optional
  optional string token = 1;

  // Audiences is a list of the identifiers that the resource server presented
  // with the token identifies as. Audience-aware token authenticators will
  // verify that the token was intended for at least one of the audiences in
  // this list. If no audiences are provided, the audience will default to the
  // audience of the Kubernetes apiserver.
  // +optional
  repeated string audiences = 2;
}

// TokenReviewStatus is the result of the token authentication request.
message TokenReviewStatus {
  // Authenticated indicates that the token was associated with a known user.
  // +optional
  optional bool authenticated = 1;

  // User is the UserInfo associated with the provided token.
  // +optional
  optional UserInfo user = 2;

  // Audiences are audience identifiers chosen by the authenticator that are
  // compatible with both the TokenReview and token. An identifier is any
  // identifier in the intersection of the TokenReviewSpec audiences and the
  // token's audiences. A client of the TokenReview API that sets the
  // spec.audiences field should validate that a compatible audience identifier
  // is returned in the status.audiences field to ensure that the TokenReview
  // server is audience aware. If a TokenReview returns an empty
  // status.audience field where status.authenticated is "true", the token is
  // valid against the audience of the Kubernetes API server.
  // +optional
  repeated string audiences = 4;

  // Error indicates that the token couldn't be checked
  // +optional
  optional string error = 3;
}

// UserInfo holds the information about the user needed to implement the
// user.Info interface.
message UserInfo {
  // The name that uniquely identifies this user among all active users.
  // +optional
  optional string username = 1;

  // A unique value that identifies this user across time. If this user is
  // deleted and another user by the same name is added, they will have
  // different UIDs.
  // +optional
  optional string uid = 2;

  // The names of groups this user is a part of.
  // +optional
  repeated string groups = 3;

  // Any additional information provided by the authenticator.
  // +optional
  map<string, ExtraValue> extra = 4;
}

