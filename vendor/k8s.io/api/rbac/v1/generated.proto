/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.rbac.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/rbac/v1";

// AggregationRule describes how to locate ClusterRoles to aggregate into the ClusterRole
message AggregationRule {
  // ClusterRoleSelectors holds a list of selectors which will be used to find ClusterRoles and create the rules.
  // If any of the selectors match, then the ClusterRole's permissions will be added
  // +optional
  repeated k8s.io.apimachinery.pkg.apis.meta.v1.LabelSelector clusterRoleSelectors = 1;
}

// ClusterRole is a cluster level, logical grouping of PolicyRules that can be referenced as a unit by a RoleBinding or ClusterRoleBinding.
message ClusterRole {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Rules holds all the PolicyRules for this ClusterRole
  // +optional
  repeated PolicyRule rules = 2;

  // AggregationRule is an optional field that describes how to build the Rules for this ClusterRole.
  // If AggregationRule is set, then the Rules are controller managed and direct changes to Rules will be
  // stomped by the controller.
  // +optional
  optional AggregationRule aggregationRule = 3;
}

// ClusterRoleBinding references a ClusterRole, but not contain it.  It can reference a ClusterRole in the global namespace,
// and adds who information via Subject.
message ClusterRoleBinding {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Subjects holds references to the objects the role applies to.
  // +optional
  repeated Subject subjects = 2;

  // RoleRef can only reference a ClusterRole in the global namespace.
  // If the RoleRef cannot be resolved, the Authorizer must return an error.
  // This field is immutable.
  optional RoleRef roleRef = 3;
}

// ClusterRoleBindingList is a collection of ClusterRoleBindings
message ClusterRoleBindingList {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of ClusterRoleBindings
  repeated ClusterRoleBinding items = 2;
}

// ClusterRoleList is a collection of ClusterRoles
message ClusterRoleList {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of ClusterRoles
  repeated ClusterRole items = 2;
}

// PolicyRule holds information that describes a policy rule, but does not contain information
// about who the rule applies to or which namespace the rule applies to.
message PolicyRule {
  // Verbs is a list of Verbs that apply to ALL the ResourceKinds contained in this rule. '*' represents all verbs.
  repeated string verbs = 1;

  // APIGroups is the name of the APIGroup that contains the resources.  If multiple API groups are specified, any action requested against one of
  // the enumerated resources in any API group will be allowed. "" represents the core API group and "*" represents all API groups.
  // +optional
  repeated string apiGroups = 2;

  // Resources is a list of resources this rule applies to. '*' represents all resources.
  // +optional
  repeated string resources = 3;

  // ResourceNames is an optional white list of names that the rule applies to.  An empty set means that everything is allowed.
  // +optional
  repeated string resourceNames = 4;

  // NonResourceURLs is a set of partial urls that a user should have access to.  *s are allowed, but only as the full, final step in the path
  // Since non-resource URLs are not namespaced, this field is only applicable for ClusterRoles referenced from a ClusterRoleBinding.
  // Rules can either apply to API resources (such as "pods" or "secrets") or non-resource URL paths (such as "/api"),  but not both.
  // +optional
  repeated string nonResourceURLs = 5;
}

// Role is a namespaced, logical grouping of PolicyRules that can be referenced as a unit by a RoleBinding.
message Role {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Rules holds all the PolicyRules for this Role
  // +optional
  repeated PolicyRule rules = 2;
}

// RoleBinding references a role, but does not contain it.  It can reference a Role in the same namespace or a ClusterRole in the global namespace.
// It adds who information via Subjects and namespace information by which namespace it exists in.  RoleBindings in a given
// namespace only have effect in that namespace.
message RoleBinding {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Subjects holds references to the objects the role applies to.
  // +optional
  repeated Subject subjects = 2;

  // RoleRef can reference a Role in the current namespace or a ClusterRole in the global namespace.
  // If the RoleRef cannot be resolved, the Authorizer must return an error.
  // This field is immutable.
  optional RoleRef roleRef = 3;
}

// RoleBindingList is a collection of RoleBindings
message RoleBindingList {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of RoleBindings
  repeated RoleBinding items = 2;
}

// RoleList is a collection of Roles
message RoleList {
  // Standard object's metadata.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  // Items is a list of Roles
  repeated Role items = 2;
}

// RoleRef contains information that points to the role being used
// +structType=atomic
message RoleRef {
  // APIGroup is the group for the resource being referenced
  optional string apiGroup = 1;

  // Kind is the type of resource being referenced
  optional string kind = 2;

  // Name is the name of resource being referenced
  optional string name = 3;
}

// Subject contains a reference to the object or user identities a role binding applies to.  This can either hold a direct API object reference,
// or a value for non-objects such as user and group names.
// +structType=atomic
message Subject {
  // Kind of object being referenced. Values defined by this API group are "User", "Group", and "ServiceAccount".
  // If the Authorizer does not recognized the kind value, the Authorizer should report an error.
  optional string kind = 1;

  // APIGroup holds the API group of the referenced subject.
  // Defaults to "" for ServiceAccount subjects.
  // Defaults to "rbac.authorization.k8s.io" for User and Group subjects.
  // +optional
  optional string apiGroup = 2;

  // Name of the object being referenced.
  optional string name = 3;

  // Namespace of the referenced object.  If the object kind is non-namespace, such as "User" or "Group", and this value is not empty
  // the Authorizer should report an error.
  // +optional
  optional string namespace = 4;
}

