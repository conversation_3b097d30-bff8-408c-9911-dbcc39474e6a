/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.certificates.v1beta1;

import "k8s.io/api/core/v1/generated.proto";
import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/certificates/v1beta1";

// Describes a certificate signing request
message CertificateSigningRequest {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // spec contains the certificate request, and is immutable after creation.
  // Only the request, signerName, expirationSeconds, and usages fields can be set on creation.
  // Other fields are derived by Kubernetes and cannot be modified by users.
  optional CertificateSigningRequestSpec spec = 2;

  // Derived information about the request.
  // +optional
  optional CertificateSigningRequestStatus status = 3;
}

message CertificateSigningRequestCondition {
  // type of the condition. Known conditions include "Approved", "Denied", and "Failed".
  optional string type = 1;

  // Status of the condition, one of True, False, Unknown.
  // Approved, Denied, and Failed conditions may not be "False" or "Unknown".
  // Defaults to "True".
  // If unset, should be treated as "True".
  // +optional
  optional string status = 6;

  // brief reason for the request state
  // +optional
  optional string reason = 2;

  // human readable message with details about the request state
  // +optional
  optional string message = 3;

  // timestamp for the last update to this condition
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastUpdateTime = 4;

  // lastTransitionTime is the time the condition last transitioned from one status to another.
  // If unset, when a new condition type is added or an existing condition's status is changed,
  // the server defaults this to the current time.
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.Time lastTransitionTime = 5;
}

message CertificateSigningRequestList {
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ListMeta metadata = 1;

  repeated CertificateSigningRequest items = 2;
}

// CertificateSigningRequestSpec contains the certificate request.
message CertificateSigningRequestSpec {
  // Base64-encoded PKCS#10 CSR data
  // +listType=atomic
  optional bytes request = 1;

  // Requested signer for the request. It is a qualified name in the form:
  // `scope-hostname.io/name`.
  // If empty, it will be defaulted:
  //  1. If it's a kubelet client certificate, it is assigned
  //     "kubernetes.io/kube-apiserver-client-kubelet".
  //  2. If it's a kubelet serving certificate, it is assigned
  //     "kubernetes.io/kubelet-serving".
  //  3. Otherwise, it is assigned "kubernetes.io/legacy-unknown".
  // Distribution of trust for signers happens out of band.
  // You can select on this field using `spec.signerName`.
  // +optional
  optional string signerName = 7;

  // expirationSeconds is the requested duration of validity of the issued
  // certificate. The certificate signer may issue a certificate with a different
  // validity duration so a client must check the delta between the notBefore and
  // and notAfter fields in the issued certificate to determine the actual duration.
  //
  // The v1.22+ in-tree implementations of the well-known Kubernetes signers will
  // honor this field as long as the requested duration is not greater than the
  // maximum duration they will honor per the --cluster-signing-duration CLI
  // flag to the Kubernetes controller manager.
  //
  // Certificate signers may not honor this field for various reasons:
  //
  //   1. Old signer that is unaware of the field (such as the in-tree
  //      implementations prior to v1.22)
  //   2. Signer whose configured maximum is shorter than the requested duration
  //   3. Signer whose configured minimum is longer than the requested duration
  //
  // The minimum valid value for expirationSeconds is 600, i.e. 10 minutes.
  //
  // +optional
  optional int32 expirationSeconds = 8;

  // allowedUsages specifies a set of usage contexts the key will be
  // valid for.
  // See:
  // 	https://tools.ietf.org/html/rfc5280#section-*******
  // 	https://tools.ietf.org/html/rfc5280#section-********
  //
  // Valid values are:
  //  "signing",
  //  "digital signature",
  //  "content commitment",
  //  "key encipherment",
  //  "key agreement",
  //  "data encipherment",
  //  "cert sign",
  //  "crl sign",
  //  "encipher only",
  //  "decipher only",
  //  "any",
  //  "server auth",
  //  "client auth",
  //  "code signing",
  //  "email protection",
  //  "s/mime",
  //  "ipsec end system",
  //  "ipsec tunnel",
  //  "ipsec user",
  //  "timestamping",
  //  "ocsp signing",
  //  "microsoft sgc",
  //  "netscape sgc"
  // +listType=atomic
  repeated string usages = 5;

  // Information about the requesting user.
  // See user.Info interface for details.
  // +optional
  optional string username = 2;

  // UID information about the requesting user.
  // See user.Info interface for details.
  // +optional
  optional string uid = 3;

  // Group information about the requesting user.
  // See user.Info interface for details.
  // +listType=atomic
  // +optional
  repeated string groups = 4;

  // Extra information about the requesting user.
  // See user.Info interface for details.
  // +optional
  map<string, ExtraValue> extra = 6;
}

message CertificateSigningRequestStatus {
  // Conditions applied to the request, such as approval or denial.
  // +listType=map
  // +listMapKey=type
  // +optional
  repeated CertificateSigningRequestCondition conditions = 1;

  // If request was approved, the controller will place the issued certificate here.
  // +listType=atomic
  // +optional
  optional bytes certificate = 2;
}

// ExtraValue masks the value so protobuf can generate
// +protobuf.nullable=true
// +protobuf.options.(gogoproto.goproto_stringer)=false
message ExtraValue {
  // items, if empty, will result in an empty slice

  repeated string items = 1;
}

