# github.com/beorn7/perks v1.0.1
## explicit; go 1.11
github.com/beorn7/perks/quantile
# github.com/caarlos0/env v3.5.0+incompatible
## explicit
github.com/caarlos0/env
# github.com/cespare/xxhash/v2 v2.3.0
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/devtron-labs/common-lib v0.19.0
## explicit; go 1.21
github.com/devtron-labs/common-lib/git-manager/util
github.com/devtron-labs/common-lib/utils
github.com/devtron-labs/common-lib/utils/bean
github.com/devtron-labs/common-lib/utils/http
github.com/devtron-labs/common-lib/utils/k8s
github.com/devtron-labs/common-lib/utils/k8s/commonBean
github.com/devtron-labs/common-lib/utils/k8sObjectsUtil
github.com/devtron-labs/common-lib/utils/remoteConnection/bean
github.com/devtron-labs/common-lib/utils/yaml
# github.com/docker/cli v24.0.6+incompatible
## explicit
github.com/docker/cli/cli/config/types
# github.com/emicklei/go-restful/v3 v3.11.0
## explicit; go 1.13
github.com/emicklei/go-restful/v3
github.com/emicklei/go-restful/v3/log
# github.com/fatih/color v1.10.0
## explicit; go 1.13
github.com/fatih/color
# github.com/felixge/httpsnoop v1.0.4
## explicit; go 1.13
github.com/felixge/httpsnoop
# github.com/fsnotify/fsnotify v1.7.0
## explicit; go 1.17
github.com/fsnotify/fsnotify
# github.com/getkin/kin-openapi v0.67.0
## explicit; go 1.14
github.com/getkin/kin-openapi/jsoninfo
github.com/getkin/kin-openapi/openapi2
github.com/getkin/kin-openapi/openapi2conv
github.com/getkin/kin-openapi/openapi3
# github.com/ghodss/yaml v1.0.0
## explicit
github.com/ghodss/yaml
# github.com/go-logr/logr v1.3.0
## explicit; go 1.18
github.com/go-logr/logr
github.com/go-logr/logr/funcr
github.com/go-logr/logr/slogr
# github.com/go-logr/stdr v1.2.2
## explicit; go 1.16
github.com/go-logr/stdr
# github.com/go-openapi/jsonpointer v0.19.6
## explicit; go 1.13
github.com/go-openapi/jsonpointer
# github.com/go-openapi/jsonreference v0.20.2
## explicit; go 1.13
github.com/go-openapi/jsonreference
github.com/go-openapi/jsonreference/internal
# github.com/go-openapi/swag v0.22.3
## explicit; go 1.18
github.com/go-openapi/swag
# github.com/gogo/protobuf v1.3.2
## explicit; go 1.15
github.com/gogo/protobuf/proto
github.com/gogo/protobuf/sortkeys
# github.com/golang/protobuf v1.5.4
## explicit; go 1.17
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/timestamp
# github.com/google/gnostic-models v0.6.8
## explicit; go 1.18
github.com/google/gnostic-models/compiler
github.com/google/gnostic-models/extensions
github.com/google/gnostic-models/jsonschema
github.com/google/gnostic-models/openapiv2
github.com/google/gnostic-models/openapiv3
# github.com/google/gofuzz v1.2.0
## explicit; go 1.12
github.com/google/gofuzz
github.com/google/gofuzz/bytesource
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/google/wire v0.6.0
## explicit; go 1.12
github.com/google/wire
# github.com/grpc-ecosystem/go-grpc-middleware/v2 v2.1.0
## explicit; go 1.19
github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/recovery
# github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0
## explicit
github.com/grpc-ecosystem/go-grpc-prometheus
# github.com/hashicorp/errwrap v1.0.0
## explicit
github.com/hashicorp/errwrap
# github.com/hashicorp/go-multierror v1.1.1
## explicit; go 1.13
github.com/hashicorp/go-multierror
# github.com/hashicorp/hcl v1.0.0
## explicit
github.com/hashicorp/hcl
github.com/hashicorp/hcl/hcl/ast
github.com/hashicorp/hcl/hcl/parser
github.com/hashicorp/hcl/hcl/printer
github.com/hashicorp/hcl/hcl/scanner
github.com/hashicorp/hcl/hcl/strconv
github.com/hashicorp/hcl/hcl/token
github.com/hashicorp/hcl/json/parser
github.com/hashicorp/hcl/json/scanner
github.com/hashicorp/hcl/json/token
# github.com/imdario/mergo v0.3.13
## explicit; go 1.13
github.com/imdario/mergo
# github.com/inconshreveable/mousetrap v1.1.0
## explicit; go 1.18
github.com/inconshreveable/mousetrap
# github.com/josharian/intern v1.0.0
## explicit; go 1.5
github.com/josharian/intern
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/magiconair/properties v1.8.1
## explicit
github.com/magiconair/properties
# github.com/mailru/easyjson v0.7.7
## explicit; go 1.12
github.com/mailru/easyjson/buffer
github.com/mailru/easyjson/jlexer
github.com/mailru/easyjson/jwriter
# github.com/mattn/go-colorable v0.1.8
## explicit; go 1.13
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.12
## explicit; go 1.12
github.com/mattn/go-isatty
# github.com/matttproud/golang_protobuf_extensions v1.0.4
## explicit; go 1.9
github.com/matttproud/golang_protobuf_extensions/pbutil
# github.com/mgutz/ansi v0.0.0-20200706080929-d51e80ef957d
## explicit
github.com/mgutz/ansi
# github.com/mitchellh/mapstructure v1.1.2
## explicit
github.com/mitchellh/mapstructure
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822
## explicit
github.com/munnerz/goautoneg
# github.com/pelletier/go-toml v1.2.0
## explicit
github.com/pelletier/go-toml
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/prometheus/client_golang v1.16.0
## explicit; go 1.17
github.com/prometheus/client_golang/prometheus
github.com/prometheus/client_golang/prometheus/internal
# github.com/prometheus/client_model v0.4.1-0.20230718164431-9a2bf3000d16
## explicit; go 1.18
github.com/prometheus/client_model/go
# github.com/prometheus/common v0.44.0
## explicit; go 1.18
github.com/prometheus/common/expfmt
github.com/prometheus/common/internal/bitbucket.org/ww/goautoneg
github.com/prometheus/common/model
# github.com/prometheus/procfs v0.11.1
## explicit; go 1.19
github.com/prometheus/procfs
github.com/prometheus/procfs/internal/fs
github.com/prometheus/procfs/internal/util
# github.com/spf13/afero v1.2.2
## explicit
github.com/spf13/afero
github.com/spf13/afero/mem
# github.com/spf13/cast v1.3.0
## explicit
github.com/spf13/cast
# github.com/spf13/cobra v1.7.0
## explicit; go 1.15
github.com/spf13/cobra
# github.com/spf13/jwalterweatherman v1.0.0
## explicit
github.com/spf13/jwalterweatherman
# github.com/spf13/pflag v1.0.5
## explicit; go 1.12
github.com/spf13/pflag
# github.com/spf13/viper v1.7.1
## explicit; go 1.12
github.com/spf13/viper
# github.com/stretchr/testify v1.8.4
## explicit; go 1.20
github.com/stretchr/testify/assert
# github.com/subosito/gotenv v1.2.0
## explicit
github.com/subosito/gotenv
# github.com/tidwall/gjson v1.9.3
## explicit; go 1.12
github.com/tidwall/gjson
# github.com/tidwall/match v1.1.1
## explicit; go 1.15
github.com/tidwall/match
# github.com/tidwall/pretty v1.2.0
## explicit; go 1.16
github.com/tidwall/pretty
# github.com/tidwall/sjson v1.1.7
## explicit; go 1.14
github.com/tidwall/sjson
# github.com/tomlazar/table v0.1.0
## explicit; go 1.15
github.com/tomlazar/table
# github.com/xeipuuv/gojsonpointer v0.0.0-20180127040702-4e3ac2762d5f
## explicit
github.com/xeipuuv/gojsonpointer
# github.com/xeipuuv/gojsonreference v0.0.0-20180127040603-bd5ef7bd5415
## explicit
github.com/xeipuuv/gojsonreference
# github.com/xeipuuv/gojsonschema v0.0.0-20180816142147-da425ebb7609
## explicit
github.com/xeipuuv/gojsonschema
# go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.45.0
## explicit; go 1.19
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/semconvutil
# go.opentelemetry.io/otel v1.19.0
## explicit; go 1.20
go.opentelemetry.io/otel
go.opentelemetry.io/otel/attribute
go.opentelemetry.io/otel/baggage
go.opentelemetry.io/otel/codes
go.opentelemetry.io/otel/internal
go.opentelemetry.io/otel/internal/attribute
go.opentelemetry.io/otel/internal/baggage
go.opentelemetry.io/otel/internal/global
go.opentelemetry.io/otel/propagation
go.opentelemetry.io/otel/semconv/v1.17.0
# go.opentelemetry.io/otel/metric v1.19.0
## explicit; go 1.20
go.opentelemetry.io/otel/metric
go.opentelemetry.io/otel/metric/embedded
# go.opentelemetry.io/otel/trace v1.19.0
## explicit; go 1.20
go.opentelemetry.io/otel/trace
# go.uber.org/atomic v1.10.0
## explicit; go 1.18
go.uber.org/atomic
# go.uber.org/multierr v1.11.0
## explicit; go 1.19
go.uber.org/multierr
# go.uber.org/zap v1.21.0
## explicit; go 1.13
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
# golang.org/x/net v0.28.0
## explicit; go 1.18
golang.org/x/net/context
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/timeseries
golang.org/x/net/trace
# golang.org/x/oauth2 v0.22.0
## explicit; go 1.18
golang.org/x/oauth2
golang.org/x/oauth2/internal
# golang.org/x/sys v0.24.0
## explicit; go 1.18
golang.org/x/sys/plan9
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/term v0.23.0
## explicit; go 1.18
golang.org/x/term
# golang.org/x/text v0.17.0
## explicit; go 1.18
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# golang.org/x/time v0.3.0
## explicit
golang.org/x/time/rate
# google.golang.org/genproto/googleapis/rpc v0.0.0-20240814211410-ddb44dafa142
## explicit; go 1.21
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.67.1
## explicit; go 1.21
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/pickfirst
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/experimental/stats
google.golang.org/grpc/grpclog
google.golang.org/grpc/grpclog/internal
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/stats
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/keepalive
google.golang.org/grpc/mem
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.34.2
## explicit; go 1.20
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/protoadapt
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/timestamppb
# gopkg.in/inf.v0 v0.9.1
## explicit
gopkg.in/inf.v0
# gopkg.in/ini.v1 v1.51.0
## explicit
gopkg.in/ini.v1
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# k8s.io/api v0.29.7
## explicit; go 1.21
k8s.io/api/admissionregistration/v1
k8s.io/api/admissionregistration/v1alpha1
k8s.io/api/admissionregistration/v1beta1
k8s.io/api/apidiscovery/v2beta1
k8s.io/api/apiserverinternal/v1alpha1
k8s.io/api/apps/v1
k8s.io/api/apps/v1beta1
k8s.io/api/apps/v1beta2
k8s.io/api/authentication/v1
k8s.io/api/authentication/v1alpha1
k8s.io/api/authentication/v1beta1
k8s.io/api/authorization/v1
k8s.io/api/authorization/v1beta1
k8s.io/api/autoscaling/v1
k8s.io/api/autoscaling/v2
k8s.io/api/autoscaling/v2beta1
k8s.io/api/autoscaling/v2beta2
k8s.io/api/batch/v1
k8s.io/api/batch/v1beta1
k8s.io/api/certificates/v1
k8s.io/api/certificates/v1alpha1
k8s.io/api/certificates/v1beta1
k8s.io/api/coordination/v1
k8s.io/api/coordination/v1beta1
k8s.io/api/core/v1
k8s.io/api/discovery/v1
k8s.io/api/discovery/v1beta1
k8s.io/api/events/v1
k8s.io/api/events/v1beta1
k8s.io/api/extensions/v1beta1
k8s.io/api/flowcontrol/v1
k8s.io/api/flowcontrol/v1beta1
k8s.io/api/flowcontrol/v1beta2
k8s.io/api/flowcontrol/v1beta3
k8s.io/api/networking/v1
k8s.io/api/networking/v1alpha1
k8s.io/api/networking/v1beta1
k8s.io/api/node/v1
k8s.io/api/node/v1alpha1
k8s.io/api/node/v1beta1
k8s.io/api/policy/v1
k8s.io/api/policy/v1beta1
k8s.io/api/rbac/v1
k8s.io/api/rbac/v1alpha1
k8s.io/api/rbac/v1beta1
k8s.io/api/resource/v1alpha2
k8s.io/api/scheduling/v1
k8s.io/api/scheduling/v1alpha1
k8s.io/api/scheduling/v1beta1
k8s.io/api/storage/v1
k8s.io/api/storage/v1alpha1
k8s.io/api/storage/v1beta1
# k8s.io/apimachinery v0.29.7
## explicit; go 1.21
k8s.io/apimachinery/pkg/api/equality
k8s.io/apimachinery/pkg/api/errors
k8s.io/apimachinery/pkg/api/meta
k8s.io/apimachinery/pkg/api/resource
k8s.io/apimachinery/pkg/api/validation
k8s.io/apimachinery/pkg/apis/meta/v1
k8s.io/apimachinery/pkg/apis/meta/v1/unstructured
k8s.io/apimachinery/pkg/apis/meta/v1/validation
k8s.io/apimachinery/pkg/conversion
k8s.io/apimachinery/pkg/conversion/queryparams
k8s.io/apimachinery/pkg/fields
k8s.io/apimachinery/pkg/labels
k8s.io/apimachinery/pkg/runtime
k8s.io/apimachinery/pkg/runtime/schema
k8s.io/apimachinery/pkg/runtime/serializer
k8s.io/apimachinery/pkg/runtime/serializer/json
k8s.io/apimachinery/pkg/runtime/serializer/protobuf
k8s.io/apimachinery/pkg/runtime/serializer/recognizer
k8s.io/apimachinery/pkg/runtime/serializer/streaming
k8s.io/apimachinery/pkg/runtime/serializer/versioning
k8s.io/apimachinery/pkg/selection
k8s.io/apimachinery/pkg/types
k8s.io/apimachinery/pkg/util/dump
k8s.io/apimachinery/pkg/util/errors
k8s.io/apimachinery/pkg/util/framer
k8s.io/apimachinery/pkg/util/intstr
k8s.io/apimachinery/pkg/util/json
k8s.io/apimachinery/pkg/util/managedfields
k8s.io/apimachinery/pkg/util/managedfields/internal
k8s.io/apimachinery/pkg/util/naming
k8s.io/apimachinery/pkg/util/net
k8s.io/apimachinery/pkg/util/runtime
k8s.io/apimachinery/pkg/util/sets
k8s.io/apimachinery/pkg/util/validation
k8s.io/apimachinery/pkg/util/validation/field
k8s.io/apimachinery/pkg/util/wait
k8s.io/apimachinery/pkg/util/yaml
k8s.io/apimachinery/pkg/version
k8s.io/apimachinery/pkg/watch
k8s.io/apimachinery/third_party/forked/golang/reflect
# k8s.io/client-go v0.29.7
## explicit; go 1.21
k8s.io/client-go/applyconfigurations/admissionregistration/v1
k8s.io/client-go/applyconfigurations/admissionregistration/v1alpha1
k8s.io/client-go/applyconfigurations/admissionregistration/v1beta1
k8s.io/client-go/applyconfigurations/apiserverinternal/v1alpha1
k8s.io/client-go/applyconfigurations/apps/v1
k8s.io/client-go/applyconfigurations/apps/v1beta1
k8s.io/client-go/applyconfigurations/apps/v1beta2
k8s.io/client-go/applyconfigurations/autoscaling/v1
k8s.io/client-go/applyconfigurations/autoscaling/v2
k8s.io/client-go/applyconfigurations/autoscaling/v2beta1
k8s.io/client-go/applyconfigurations/autoscaling/v2beta2
k8s.io/client-go/applyconfigurations/batch/v1
k8s.io/client-go/applyconfigurations/batch/v1beta1
k8s.io/client-go/applyconfigurations/certificates/v1
k8s.io/client-go/applyconfigurations/certificates/v1alpha1
k8s.io/client-go/applyconfigurations/certificates/v1beta1
k8s.io/client-go/applyconfigurations/coordination/v1
k8s.io/client-go/applyconfigurations/coordination/v1beta1
k8s.io/client-go/applyconfigurations/core/v1
k8s.io/client-go/applyconfigurations/discovery/v1
k8s.io/client-go/applyconfigurations/discovery/v1beta1
k8s.io/client-go/applyconfigurations/events/v1
k8s.io/client-go/applyconfigurations/events/v1beta1
k8s.io/client-go/applyconfigurations/extensions/v1beta1
k8s.io/client-go/applyconfigurations/flowcontrol/v1
k8s.io/client-go/applyconfigurations/flowcontrol/v1beta1
k8s.io/client-go/applyconfigurations/flowcontrol/v1beta2
k8s.io/client-go/applyconfigurations/flowcontrol/v1beta3
k8s.io/client-go/applyconfigurations/internal
k8s.io/client-go/applyconfigurations/meta/v1
k8s.io/client-go/applyconfigurations/networking/v1
k8s.io/client-go/applyconfigurations/networking/v1alpha1
k8s.io/client-go/applyconfigurations/networking/v1beta1
k8s.io/client-go/applyconfigurations/node/v1
k8s.io/client-go/applyconfigurations/node/v1alpha1
k8s.io/client-go/applyconfigurations/node/v1beta1
k8s.io/client-go/applyconfigurations/policy/v1
k8s.io/client-go/applyconfigurations/policy/v1beta1
k8s.io/client-go/applyconfigurations/rbac/v1
k8s.io/client-go/applyconfigurations/rbac/v1alpha1
k8s.io/client-go/applyconfigurations/rbac/v1beta1
k8s.io/client-go/applyconfigurations/resource/v1alpha2
k8s.io/client-go/applyconfigurations/scheduling/v1
k8s.io/client-go/applyconfigurations/scheduling/v1alpha1
k8s.io/client-go/applyconfigurations/scheduling/v1beta1
k8s.io/client-go/applyconfigurations/storage/v1
k8s.io/client-go/applyconfigurations/storage/v1alpha1
k8s.io/client-go/applyconfigurations/storage/v1beta1
k8s.io/client-go/discovery
k8s.io/client-go/discovery/cached/memory
k8s.io/client-go/dynamic
k8s.io/client-go/kubernetes
k8s.io/client-go/kubernetes/scheme
k8s.io/client-go/kubernetes/typed/admissionregistration/v1
k8s.io/client-go/kubernetes/typed/admissionregistration/v1alpha1
k8s.io/client-go/kubernetes/typed/admissionregistration/v1beta1
k8s.io/client-go/kubernetes/typed/apiserverinternal/v1alpha1
k8s.io/client-go/kubernetes/typed/apps/v1
k8s.io/client-go/kubernetes/typed/apps/v1beta1
k8s.io/client-go/kubernetes/typed/apps/v1beta2
k8s.io/client-go/kubernetes/typed/authentication/v1
k8s.io/client-go/kubernetes/typed/authentication/v1alpha1
k8s.io/client-go/kubernetes/typed/authentication/v1beta1
k8s.io/client-go/kubernetes/typed/authorization/v1
k8s.io/client-go/kubernetes/typed/authorization/v1beta1
k8s.io/client-go/kubernetes/typed/autoscaling/v1
k8s.io/client-go/kubernetes/typed/autoscaling/v2
k8s.io/client-go/kubernetes/typed/autoscaling/v2beta1
k8s.io/client-go/kubernetes/typed/autoscaling/v2beta2
k8s.io/client-go/kubernetes/typed/batch/v1
k8s.io/client-go/kubernetes/typed/batch/v1beta1
k8s.io/client-go/kubernetes/typed/certificates/v1
k8s.io/client-go/kubernetes/typed/certificates/v1alpha1
k8s.io/client-go/kubernetes/typed/certificates/v1beta1
k8s.io/client-go/kubernetes/typed/coordination/v1
k8s.io/client-go/kubernetes/typed/coordination/v1beta1
k8s.io/client-go/kubernetes/typed/core/v1
k8s.io/client-go/kubernetes/typed/discovery/v1
k8s.io/client-go/kubernetes/typed/discovery/v1beta1
k8s.io/client-go/kubernetes/typed/events/v1
k8s.io/client-go/kubernetes/typed/events/v1beta1
k8s.io/client-go/kubernetes/typed/extensions/v1beta1
k8s.io/client-go/kubernetes/typed/flowcontrol/v1
k8s.io/client-go/kubernetes/typed/flowcontrol/v1beta1
k8s.io/client-go/kubernetes/typed/flowcontrol/v1beta2
k8s.io/client-go/kubernetes/typed/flowcontrol/v1beta3
k8s.io/client-go/kubernetes/typed/networking/v1
k8s.io/client-go/kubernetes/typed/networking/v1alpha1
k8s.io/client-go/kubernetes/typed/networking/v1beta1
k8s.io/client-go/kubernetes/typed/node/v1
k8s.io/client-go/kubernetes/typed/node/v1alpha1
k8s.io/client-go/kubernetes/typed/node/v1beta1
k8s.io/client-go/kubernetes/typed/policy/v1
k8s.io/client-go/kubernetes/typed/policy/v1beta1
k8s.io/client-go/kubernetes/typed/rbac/v1
k8s.io/client-go/kubernetes/typed/rbac/v1alpha1
k8s.io/client-go/kubernetes/typed/rbac/v1beta1
k8s.io/client-go/kubernetes/typed/resource/v1alpha2
k8s.io/client-go/kubernetes/typed/scheduling/v1
k8s.io/client-go/kubernetes/typed/scheduling/v1alpha1
k8s.io/client-go/kubernetes/typed/scheduling/v1beta1
k8s.io/client-go/kubernetes/typed/storage/v1
k8s.io/client-go/kubernetes/typed/storage/v1alpha1
k8s.io/client-go/kubernetes/typed/storage/v1beta1
k8s.io/client-go/openapi
k8s.io/client-go/openapi/cached
k8s.io/client-go/pkg/apis/clientauthentication
k8s.io/client-go/pkg/apis/clientauthentication/install
k8s.io/client-go/pkg/apis/clientauthentication/v1
k8s.io/client-go/pkg/apis/clientauthentication/v1beta1
k8s.io/client-go/pkg/version
k8s.io/client-go/plugin/pkg/client/auth
k8s.io/client-go/plugin/pkg/client/auth/azure
k8s.io/client-go/plugin/pkg/client/auth/exec
k8s.io/client-go/plugin/pkg/client/auth/gcp
k8s.io/client-go/plugin/pkg/client/auth/oidc
k8s.io/client-go/rest
k8s.io/client-go/rest/watch
k8s.io/client-go/restmapper
k8s.io/client-go/tools/auth
k8s.io/client-go/tools/clientcmd
k8s.io/client-go/tools/clientcmd/api
k8s.io/client-go/tools/clientcmd/api/latest
k8s.io/client-go/tools/clientcmd/api/v1
k8s.io/client-go/tools/metrics
k8s.io/client-go/tools/reference
k8s.io/client-go/transport
k8s.io/client-go/util/cert
k8s.io/client-go/util/connrotation
k8s.io/client-go/util/flowcontrol
k8s.io/client-go/util/homedir
k8s.io/client-go/util/keyutil
k8s.io/client-go/util/workqueue
# k8s.io/klog/v2 v2.110.1
## explicit; go 1.13
k8s.io/klog/v2
k8s.io/klog/v2/internal/buffer
k8s.io/klog/v2/internal/clock
k8s.io/klog/v2/internal/dbg
k8s.io/klog/v2/internal/serialize
k8s.io/klog/v2/internal/severity
k8s.io/klog/v2/internal/sloghandler
# k8s.io/kube-openapi v0.0.0-20231010175941-2dd684a91f00
## explicit; go 1.19
k8s.io/kube-openapi/pkg/cached
k8s.io/kube-openapi/pkg/common
k8s.io/kube-openapi/pkg/handler3
k8s.io/kube-openapi/pkg/internal
k8s.io/kube-openapi/pkg/internal/third_party/go-json-experiment/json
k8s.io/kube-openapi/pkg/schemaconv
k8s.io/kube-openapi/pkg/spec3
k8s.io/kube-openapi/pkg/util/proto
k8s.io/kube-openapi/pkg/validation/spec
# k8s.io/kubernetes v1.29.6
## explicit; go 1.21
k8s.io/kubernetes/pkg/api/legacyscheme
# k8s.io/metrics v0.29.7
## explicit; go 1.21
k8s.io/metrics/pkg/apis/metrics
k8s.io/metrics/pkg/apis/metrics/v1alpha1
k8s.io/metrics/pkg/apis/metrics/v1beta1
k8s.io/metrics/pkg/client/clientset/versioned
k8s.io/metrics/pkg/client/clientset/versioned/scheme
k8s.io/metrics/pkg/client/clientset/versioned/typed/metrics/v1alpha1
k8s.io/metrics/pkg/client/clientset/versioned/typed/metrics/v1beta1
# k8s.io/utils v0.0.0-20230726121419-3b25d923346b
## explicit; go 1.18
k8s.io/utils/clock
k8s.io/utils/clock/testing
k8s.io/utils/integer
k8s.io/utils/internal/third_party/forked/golang/net
k8s.io/utils/net
k8s.io/utils/pointer
k8s.io/utils/ptr
k8s.io/utils/strings/slices
# sigs.k8s.io/json v0.0.0-20221116044647-bc3834ca7abd
## explicit; go 1.18
sigs.k8s.io/json
sigs.k8s.io/json/internal/golang/encoding/json
# sigs.k8s.io/structured-merge-diff/v4 v4.4.1
## explicit; go 1.13
sigs.k8s.io/structured-merge-diff/v4/fieldpath
sigs.k8s.io/structured-merge-diff/v4/merge
sigs.k8s.io/structured-merge-diff/v4/schema
sigs.k8s.io/structured-merge-diff/v4/typed
sigs.k8s.io/structured-merge-diff/v4/value
# sigs.k8s.io/yaml v1.3.0
## explicit; go 1.12
sigs.k8s.io/yaml
