language: go
go:
  - 1.5
  - 1.6
  - 1.7
  - 1.8
  - 1.9
  - '1.10.x'
  - '1.11.x'
  - tip
before_install:
  - go get github.com/axw/gocov/gocov
  - go get github.com/mattn/goveralls
  - go get golang.org/x/tools/cmd/cover
script:
  - go test -v -cover -race -coverprofile=coverage.out
after_script:
  - go get github.com/mattn/goveralls
  - goveralls -coverprofile=coverage.out -service=travis-ci -repotoken='eCcizKmTdSaJCz8Ih33WDppdqb9kioYwi'
