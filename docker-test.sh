#!/bin/bash

# Docker-based testing for Silver Surfer gRPC server
echo "🐳 Setting up Docker-based testing environment"

# Build the server image
echo "Building Silver Surfer server image..."
docker build -f app/DockerfileMVC -t silver-surfer-server .

if [ $? -ne 0 ]; then
    echo "❌ Failed to build server image"
    exit 1
fi

echo "✅ Server image built successfully"

# Start the server container
echo "Starting server container..."
docker run -d --name silver-surfer-test -p 8111:8111 silver-surfer-server

# Wait for server to start
echo "Waiting for server to start..."
sleep 5

# Check if container is running
if ! docker ps | grep -q silver-surfer-test; then
    echo "❌ Server container failed to start"
    docker logs silver-surfer-test
    exit 1
fi

echo "✅ Server container is running"

# Test connectivity
echo "Testing server connectivity..."
if curl -f http://localhost:8111 2>/dev/null; then
    echo "✅ Server is responding"
else
    echo "⚠️  HTTP check failed (expected for gRPC server)"
fi

# Run grpcurl tests if available
if command -v grpcurl &> /dev/null; then
    echo "Running grpcurl tests..."
    grpcurl -plaintext localhost:8111 list
else
    echo "⚠️  grpcurl not available for testing"
fi

# Cleanup function
cleanup() {
    echo "Cleaning up..."
    docker stop silver-surfer-test
    docker rm silver-surfer-test
}

# Set trap for cleanup
trap cleanup EXIT

echo "✅ Docker test setup completed!"
echo "Server is running at localhost:8111"
echo "Press Ctrl+C to stop and cleanup"

# Keep script running
while true; do
    sleep 1
done
