package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	
	// Import the generated gRPC client
	pb "github.com/devtron-labs/silver-surfer/app/grpc"
)

func main() {
	// Connect to the gRPC server
	conn, err := grpc.Dial("localhost:8111", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to server: %v", err)
	}
	defer conn.Close()

	// Create a client
	client := pb.NewSilverSurferServiceClient(conn)

	// Test 1: Basic connectivity test with minimal request
	fmt.Println("🧪 Test 1: Basic Connectivity Test")
	testBasicConnectivity(client)

	// Test 2: Test with sample cluster config
	fmt.Println("\n🧪 Test 2: Sample Cluster Validation Test")
	testSampleClusterValidation(client)

	fmt.Println("\n✅ All tests completed!")
}

func testBasicConnectivity(client pb.SilverSurferServiceClient) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Create a minimal request
	request := &pb.ClusterUpgradeRequest{
		TargetK8SVersion: "1.25",
		ClusterConfig: &pb.ClusterConfig{
			ApiServerUrl: "https://test-cluster.example.com",
			ClusterId:    1,
			ClusterName:  "test-cluster",
			InsecureSkipTLSVerify: true,
		},
	}

	fmt.Printf("Sending request to server...\n")
	response, err := client.GetClusterUpgradeSummaryValidationResult(ctx, request)
	
	if err != nil {
		fmt.Printf("❌ Request failed: %v\n", err)
		return
	}

	fmt.Printf("✅ Server responded successfully!\n")
	fmt.Printf("📊 Response contains %d validation results\n", len(response.Results))
}

func testSampleClusterValidation(client pb.SilverSurferServiceClient) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Create a more comprehensive test request
	request := &pb.ClusterUpgradeRequest{
		TargetK8SVersion: "1.26",
		ClusterConfig: &pb.ClusterConfig{
			ApiServerUrl:          "https://kubernetes.default.svc",
			ClusterId:            123,
			ClusterName:          "production-cluster",
			InsecureSkipTLSVerify: true,
			Token:                "sample-token", // This would be a real token in production
			RemoteConnectionConfig: &pb.RemoteConnectionConfig{
				RemoteConnectionMethod: pb.RemoteConnectionMethod_DIRECT,
			},
		},
	}

	fmt.Printf("Testing cluster upgrade validation for K8s version %s...\n", request.TargetK8SVersion)
	
	response, err := client.GetClusterUpgradeSummaryValidationResult(ctx, request)
	
	if err != nil {
		fmt.Printf("❌ Validation request failed: %v\n", err)
		return
	}

	fmt.Printf("✅ Validation completed successfully!\n")
	fmt.Printf("📊 Found %d validation results:\n", len(response.Results))
	
	// Display summary of results
	for i, result := range response.Results {
		if i >= 5 { // Limit output to first 5 results
			fmt.Printf("... and %d more results\n", len(response.Results)-5)
			break
		}
		
		fmt.Printf("  %d. %s/%s (%s)\n", i+1, result.Kind, result.ResourceName, result.APIVersion)
		if result.Deprecated {
			fmt.Printf("     ⚠️  Deprecated API\n")
		}
		if result.Deleted {
			fmt.Printf("     🗑️  API will be removed\n")
		}
		if len(result.ErrorsForOriginal) > 0 {
			fmt.Printf("     ❌ %d validation errors\n", len(result.ErrorsForOriginal))
		}
	}
}
